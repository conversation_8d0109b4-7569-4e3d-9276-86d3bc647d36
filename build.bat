@echo off
echo ========================================
echo Gann Square of 9 Calculator Builder
echo ========================================
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed or not in PATH!
    echo Please install Java Development Kit (JDK) 8 or higher.
    echo Download from: https://www.oracle.com/java/technologies/downloads/
    echo.
    pause
    exit /b 1
)

REM Check if javac is available
javac -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java compiler (javac) is not available!
    echo Please install Java Development Kit (JDK), not just JRE.
    echo.
    pause
    exit /b 1
)

echo Step 1: Compiling Java source code...
javac GannSquareOf9Calculator.java
if %errorlevel% neq 0 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)
echo ✓ Compilation successful!

echo.
echo Step 2: Creating JAR file...
jar cfm GannSquareOf9Calculator.jar MANIFEST.MF *.class
if %errorlevel% neq 0 (
    echo ERROR: JAR creation failed!
    pause
    exit /b 1
)
echo ✓ JAR file created successfully!

echo.
echo Step 3: Testing the JAR file...
echo Starting the application...
start java -jar GannSquareOf9Calculator.jar

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Files created:
echo - GannSquareOf9Calculator.class (compiled Java bytecode)
echo - GannSquareOf9Calculator.jar (executable JAR file)
echo.
echo To run the application:
echo 1. Double-click GannSquareOf9Calculator.jar
echo 2. Or run: java -jar GannSquareOf9Calculator.jar
echo 3. Or use the run.bat file
echo.
pause
