<?xml version="1.0" encoding="UTF-8"?>
<launch4jConfig>
  <dontWrapJar>false</dontWrapJar>
  <headerType>gui</headerType>
  <jar>GannSquareOf9Calculator.jar</jar>
  <outfile>GannSquareOf9Calculator.exe</outfile>
  <errTitle>Gann Square of 9 Calculator</errTitle>
  <cmdLine></cmdLine>
  <chdir>.</chdir>
  <priority>normal</priority>
  <downloadUrl>https://www.java.com/download/</downloadUrl>
  <supportUrl>https://www.java.com/download/</supportUrl>
  <stayAlive>false</stayAlive>
  <restartOnCrash>false</restartOnCrash>
  <manifest></manifest>
  <icon></icon>
  <jre>
    <path></path>
    <bundledJre64Bit>false</bundledJre64Bit>
    <bundledJreAsFallback>false</bundledJreAsFallback>
    <minVersion>1.8.0</minVersion>
    <maxVersion></maxVersion>
    <jdkPreference>preferJre</jdkPreference>
    <runtimeBits>64/32</runtimeBits>
  </jre>
  <versionInfo>
    <fileVersion>*******</fileVersion>
    <txtFileVersion>1.0.0</txtFileVersion>
    <fileDescription>Gann Square of 9 Calculator - Financial Analysis Tool</fileDescription>
    <copyright>2024</copyright>
    <productVersion>*******</productVersion>
    <txtProductVersion>1.0.0</txtProductVersion>
    <productName>Gann Square of 9 Calculator</productName>
    <companyName></companyName>
    <internalName>GannSquareOf9Calculator</internalName>
    <originalFilename>GannSquareOf9Calculator.exe</originalFilename>
  </versionInfo>
</launch4jConfig>
