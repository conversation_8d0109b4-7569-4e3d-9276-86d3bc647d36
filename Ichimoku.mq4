//+------------------------------------------------------------------+
//|                                                     Ichimoku.mq4 |
//|                             Copyright 2000-2025, MetaQuotes Ltd. |
//|                                              http://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright   "2000-2025, MetaQuotes Ltd."
#property link        "https://www.mql5.com"
#property description "Ichimoku Kinko Hyo"
#property strict

#property indicator_chart_window
#property indicator_buffers 9
#property indicator_color1 CLR_NONE     // Tenkan-sen (hidden)
#property indicator_color2 CLR_NONE     // Kijun-sen (hidden)
#property indicator_color3 CLR_NONE     // Up Kumo (hidden)
#property indicator_color4 CLR_NONE     // Down Kumo (hidden)
#property indicator_color5 CLR_NONE     // Chi<PERSON>u Span (hidden)
#property indicator_color6 CLR_NONE     // Up Kumo bounding line (hidden)
#property indicator_color7 CLR_NONE     // Down Kumo bounding line (hidden)
#property indicator_color8 Lime         // Buy Signal (Up Arrow)
#property indicator_color9 Red          // Sell Signal (Down Arrow)
//--- input parameters
input int InpTenkan=9;   // Tenkan-sen
input int InpKijun=26;   // Kijun-sen
input int InpSenkou=52;  // Senkou Span B
input bool ShowArrows=true;  // Show Signal Arrows
input int ArrowSize=5;       // Arrow Size (1-5)
input int ArrowDistance=10;  // Arrow Distance from Candle (in points)
//--- buffers
double ExtTenkanBuffer[];
double ExtKijunBuffer[];
double ExtSpanA_Buffer[];
double ExtSpanB_Buffer[];
double ExtChikouBuffer[];
double ExtSpanA2_Buffer[];
double ExtSpanB2_Buffer[];
double ExtBuyBuffer[];     // Buffer for buy signals (up arrows)
double ExtSellBuffer[];    // Buffer for sell signals (down arrows)
//---
int    ExtBegin;
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
void OnInit(void)
  {
   IndicatorDigits(Digits);
//---
   SetIndexStyle(0,DRAW_NONE);  // Hide Tenkan-sen
   SetIndexBuffer(0,ExtTenkanBuffer);
   SetIndexDrawBegin(0,InpTenkan-1);
   SetIndexLabel(0,"Tenkan Sen");
//---
   SetIndexStyle(1,DRAW_NONE);  // Hide Kijun-sen
   SetIndexBuffer(1,ExtKijunBuffer);
   SetIndexDrawBegin(1,InpKijun-1);
   SetIndexLabel(1,"Kijun Sen");
//---
   ExtBegin=InpKijun;
   if(ExtBegin<InpTenkan)
      ExtBegin=InpTenkan;
//---
   SetIndexStyle(2,DRAW_NONE);  // Hide Kumo (cloud)
   SetIndexBuffer(2,ExtSpanA_Buffer);
   SetIndexDrawBegin(2,ExtBegin-1);
   SetIndexShift(2,0); // Changed from InpKijun to 0
   SetIndexLabel(2,NULL);
   SetIndexStyle(5,DRAW_NONE);  // Hide Senkou Span A line
   SetIndexBuffer(5,ExtSpanA2_Buffer);
   SetIndexDrawBegin(5,ExtBegin-1);
   SetIndexShift(5,0); // Changed from InpKijun to 0
   SetIndexLabel(5,"Senkou Span A");
//---
   SetIndexStyle(3,DRAW_NONE);  // Hide Kumo (cloud)
   SetIndexBuffer(3,ExtSpanB_Buffer);
   SetIndexDrawBegin(3,InpSenkou-1);
   SetIndexShift(3,0); // Changed from InpKijun to 0
   SetIndexLabel(3,NULL);
   SetIndexStyle(6,DRAW_NONE);  // Hide Senkou Span B line
   SetIndexBuffer(6,ExtSpanB2_Buffer);
   SetIndexDrawBegin(6,InpSenkou-1);
   SetIndexShift(6,0); // Changed from InpKijun to 0
   SetIndexLabel(6,"Senkou Span B");
//---
   SetIndexStyle(4,DRAW_NONE);  // Hide Chikou Span
   SetIndexBuffer(4,ExtChikouBuffer);
   SetIndexShift(4,-InpKijun);
   SetIndexLabel(4,"Chikou Span");
//--- Set up buy signal buffer (up arrows)
   SetIndexStyle(7,DRAW_ARROW,STYLE_SOLID,ArrowSize,indicator_color8);
   SetIndexBuffer(7,ExtBuyBuffer);
   SetIndexArrow(7,233);  // Up arrow
   SetIndexLabel(7,"Buy Signal");
   SetIndexEmptyValue(7,0.0);
//--- Set up sell signal buffer (down arrows)
   SetIndexStyle(8,DRAW_ARROW,STYLE_SOLID,ArrowSize,indicator_color9);
   SetIndexBuffer(8,ExtSellBuffer);
   SetIndexArrow(8,234);  // Down arrow
   SetIndexLabel(8,"Sell Signal");
   SetIndexEmptyValue(8,0.0);
//--- initialization done
  }
//+------------------------------------------------------------------+
//| Ichimoku Kinko Hyo                                               |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   int    i,k,pos;
   double high_value,low_value;
//---
   if(rates_total<=InpTenkan || rates_total<=InpKijun || rates_total<=InpSenkou)
      return(0);
//--- counting from 0 to rates_total
   ArraySetAsSeries(ExtTenkanBuffer,false);
   ArraySetAsSeries(ExtKijunBuffer,false);
   ArraySetAsSeries(ExtSpanA_Buffer,false);
   ArraySetAsSeries(ExtSpanB_Buffer,false);
   ArraySetAsSeries(ExtChikouBuffer,false);
   ArraySetAsSeries(ExtSpanA2_Buffer,false);
   ArraySetAsSeries(ExtSpanB2_Buffer,false);
   ArraySetAsSeries(ExtBuyBuffer,false);
   ArraySetAsSeries(ExtSellBuffer,false);
   ArraySetAsSeries(open,false);
   ArraySetAsSeries(high,false);
   ArraySetAsSeries(low,false);
   ArraySetAsSeries(close,false);
//--- initial zero
   if(prev_calculated<1)
     {
      for(i=0; i<InpTenkan; i++)
         ExtTenkanBuffer[i]=0.0;
      for(i=0; i<InpKijun; i++)
         ExtKijunBuffer[i]=0.0;
      for(i=0; i<ExtBegin; i++)
        {
         ExtSpanA_Buffer[i]=0.0;
         ExtSpanA2_Buffer[i]=0.0;
        }
      for(i=0; i<InpSenkou; i++)
        {
         ExtSpanB_Buffer[i]=0.0;
         ExtSpanB2_Buffer[i]=0.0;
        }
      // Initialize arrow buffers
      for(i=0; i<rates_total; i++)
        {
         ExtBuyBuffer[i]=0.0;
         ExtSellBuffer[i]=0.0;
        }
     }
//--- Tenkan Sen
   pos=InpTenkan-1;
   if(prev_calculated>InpTenkan)
      pos=prev_calculated-1;
   for(i=pos; i<rates_total; i++)
     {
      high_value=high[i];
      low_value=low[i];
      k=i+1-InpTenkan;
      while(k<=i)
        {
         if(high_value<high[k])
            high_value=high[k];
         if(low_value>low[k])
            low_value=low[k];
         k++;
        }
      ExtTenkanBuffer[i]=(high_value+low_value)/2;
     }
//--- Kijun Sen
   pos=InpKijun-1;
   if(prev_calculated>InpKijun)
      pos=prev_calculated-1;
   for(i=pos; i<rates_total; i++)
     {
      high_value=high[i];
      low_value=low[i];
      k=i+1-InpKijun;
      while(k<=i)
        {
         if(high_value<high[k])
            high_value=high[k];
         if(low_value>low[k])
            low_value=low[k];
         k++;
        }
      ExtKijunBuffer[i]=(high_value+low_value)/2;
     }
//--- Senkou Span A
   pos=ExtBegin-1;
   if(prev_calculated>ExtBegin)
      pos=prev_calculated-1;
   for(i=pos; i<rates_total; i++)
     {
      ExtSpanA_Buffer[i]=(ExtKijunBuffer[i]+ExtTenkanBuffer[i])/2;
      ExtSpanA2_Buffer[i]=ExtSpanA_Buffer[i];
     }
//--- Senkou Span B
   pos=InpSenkou-1;
   if(prev_calculated>InpSenkou)
      pos=prev_calculated-1;
   for(i=pos; i<rates_total; i++)
     {
      high_value=high[i];
      low_value=low[i];
      k=i+1-InpSenkou;
      while(k<=i)
        {
         if(high_value<high[k])
            high_value=high[k];
         if(low_value>low[k])
            low_value=low[k];
         k++;
        }
      ExtSpanB_Buffer[i]=(high_value+low_value)/2;
      ExtSpanB2_Buffer[i]=ExtSpanB_Buffer[i];
     }
//--- Chikou Span
   pos=0;
   if(prev_calculated>1)
      pos=prev_calculated-1;
   for(i=pos; i<rates_total; i++)
      ExtChikouBuffer[i]=close[i];

//--- Calculate buy/sell signals based on Senkou Span A position relative to previous
   if(ShowArrows)
     {
      // Start from the second bar to compare with previous
      int start=1; // Need at least 1 previous bar
      if(prev_calculated>1)
         start=prev_calculated-1;

      for(i=start; i<rates_total; i++)
        {
         // Clear previous values
         ExtBuyBuffer[i]=0.0;
         ExtSellBuffer[i]=0.0;

         // Check if current bar has valid Senkou Span A value
         if(i>0) // Need at least 1 previous bar
           {
            // Get current and previous Senkou Span A values
            double currentSpanA=ExtSpanA2_Buffer[i];
            double prevSpanA=ExtSpanA2_Buffer[i-1];

            // Compare current Senkou Span A with previous
            if(currentSpanA>prevSpanA) // Current Senkou Span A is above previous
              {
               // Place buy arrow below the candle
               ExtBuyBuffer[i]=low[i]-ArrowDistance*Point;

               // Debug info
               // Print("Buy Signal at bar: ", i, " Time: ", TimeToStr(time[i]), " Low: ", low[i], " SpanA: ", currentSpanA);
              }
            else if(currentSpanA<prevSpanA) // Current Senkou Span A is below previous
              {
               // Place sell arrow above the candle
               ExtSellBuffer[i]=high[i]+ArrowDistance*Point;

               // Debug info
               // Print("Sell Signal at bar: ", i, " Time: ", TimeToStr(time[i]), " High: ", high[i], " SpanA: ", currentSpanA);
              }
           }
        }
     }
//---
   return(rates_total);
  }
//+------------------------------------------------------------------+
