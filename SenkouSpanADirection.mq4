//+------------------------------------------------------------------+
//|                                          SenkouSpanADirection.mq4 |
//|                                                                   |
//| This indicator generates buy and sell signals based on the        |
//| direction of the Senkou Span A line from the Ichimoku indicator.  |
//| - Up arrow: When Senkou Span A is moving upward                   |
//| - Down arrow: When Senkou Span A is moving downward               |
//| The indicator looks at the Senkou Span A line that is 26 candles  |
//| ahead of the current candle to determine the direction.           |
//| Arrows are displayed on every candle based on the direction.      |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023"
#property link      ""
#property version   "1.00"
#property strict
#property indicator_chart_window
#property indicator_buffers 3
#property indicator_color1 Lime
#property indicator_color2 Red
#property indicator_width1 2
#property indicator_width2 2

// Input parameters
extern int IchimokuTenkanSen = 9;       // Tenkan-sen period
extern int IchimokuKijunSen = 26;       // Kijun-sen period
extern int IchimokuSenkouSpanB = 52;    // Senkou Span B period
extern int ArrowSize = 4;               // Size of the arrow
extern int ArrowDistance = 15;         // Distance of arrow from candle high/low in points

// Buffers
double UpArrowBuffer[];
double DownArrowBuffer[];
double DirectionBuffer[]; // Buffer to store the direction of Senkou Span A

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   // Set indicator buffers
   SetIndexBuffer(0, UpArrowBuffer);
   SetIndexBuffer(1, DownArrowBuffer);
   SetIndexBuffer(2, DirectionBuffer);

   // Set indicator labels
   SetIndexLabel(0, "Up Arrow");
   SetIndexLabel(1, "Down Arrow");
   SetIndexLabel(2, "Direction");

   // Set empty values
   SetIndexEmptyValue(0, 0.0);
   SetIndexEmptyValue(1, 0.0);

   // Hide the direction buffer from the chart
   SetIndexStyle(2, DRAW_NONE);

   // Set indicator styles and colors
   SetIndexStyle(0, DRAW_ARROW, STYLE_SOLID, ArrowSize, clrLime);
   SetIndexStyle(1, DRAW_ARROW, STYLE_SOLID, ArrowSize, clrRed);

   // Set arrow codes
   SetIndexArrow(0, 233); // Up arrow
   SetIndexArrow(1, 234); // Down arrow

   // Set indicator name
   IndicatorShortName("Senkou Span A Direction");

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   // Reset the calculation flag at the beginning of each call
   static bool calculated = false;
   static int direction = 0; // 0 = undefined, 1 = up, -1 = down
   calculated = false;

   // Check for minimum required bars
   // We need enough bars for Ichimoku calculation plus 26 more for the displacement
   if(rates_total < IchimokuSenkouSpanB + 26 + 1) return(0);

   // Calculate start position
   int limit;
   if(prev_calculated == 0)
      limit = rates_total - IchimokuSenkouSpanB - 26 - 1;
   else
      limit = rates_total - prev_calculated;

   // Ensure we don't recalculate all bars
   if(limit > rates_total - 1) limit = rates_total - 1;

   // Main calculation loop
   for(int i = limit; i >= 0; i--)
   {
      // Clear previous values
      UpArrowBuffer[i] = 0;
      DownArrowBuffer[i] = 0;

      // Get Senkou Span A values
      // We need to check the direction of the Senkou Span A line at position 26
      // (26 candles in the future) to determine whether to place up or down arrows
      // on the current candle (position 0)

      // For the current calculation, we only need to check the Senkou Span A at position 26
      // and place arrows on all candles based on that direction

      // Only calculate the direction once per indicator update
      if(!calculated && rates_total > 26)
      {
         // Get the Senkou Span A values at position 26 (26 candles in the future)
         double currentSenkouA = iIchimoku(NULL, 0, IchimokuTenkanSen, IchimokuKijunSen, IchimokuSenkouSpanB, MODE_SENKOUSPANA, 26);
         double prevSenkouA = iIchimoku(NULL, 0, IchimokuTenkanSen, IchimokuKijunSen, IchimokuSenkouSpanB, MODE_SENKOUSPANA, 27);

         // Determine direction of Senkou Span A at position 26
         if(currentSenkouA > prevSenkouA)
         {
            direction = 1; // Up direction
         }
         else if(currentSenkouA < prevSenkouA)
         {
            direction = -1; // Down direction
         }

         calculated = true;
      }

      // Store the direction in the buffer for reference
      DirectionBuffer[i] = direction;

      // Place arrows based on the direction of Senkou Span A at position 26
      if(direction > 0)
      {
         // Senkou Span A is moving up - place up arrow above the candle
         UpArrowBuffer[i] = High[i] + ArrowDistance * Point;
      }
      else if(direction < 0)
      {
         // Senkou Span A is moving down - place down arrow above the candle
         DownArrowBuffer[i] = High[i] + ArrowDistance * Point;
      }
   }

   return(rates_total);
}
