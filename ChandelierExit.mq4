//+------------------------------------------------------------------+
//|                                                ChandelierExit.mq4 |
//|                                      Converted from TradingView   |
//|                                                                   |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2023"
#property link      ""
#property version   "1.00"
#property strict
#property indicator_chart_window
#property indicator_buffers 4
#property indicator_color1 Green
#property indicator_color2 Red
#property indicator_width1 2
#property indicator_width2 2
#property indicator_style1 STYLE_SOLID
#property indicator_style2 STYLE_SOLID

// Input parameters
extern int    ATR_Period = 22;       // ATR Period
extern double ATR_Multiplier = 3.0;   // ATR Multiplier
extern bool   UseClosePrice = true;   // Use Close Price for Extremums
extern bool   ShowLabels = true;      // Show Buy/Sell Labels
extern bool   HighlightState = true;  // Highlight State

// Buffers
double LongStopBuffer[];
double ShortStopBuffer[];
double BuySignalBuffer[];
double SellSignalBuffer[];

// Global variables
int Direction = 1;  // 1 for long, -1 for short
int LastSignal = 0; // 0 for none, 1 for buy, -1 for sell

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   // Set indicator buffers
   SetIndexBuffer(0, LongStopBuffer);
   SetIndexBuffer(1, ShortStopBuffer);
   SetIndexBuffer(2, BuySignalBuffer);
   SetIndexBuffer(3, SellSignalBuffer);

   // Set indicator labels
   SetIndexLabel(0, "Long Stop");
   SetIndexLabel(1, "Short Stop");
   SetIndexLabel(2, "Buy Signal");
   SetIndexLabel(3, "Sell Signal");

   // Set empty values
   SetIndexEmptyValue(0, 0.0);
   SetIndexEmptyValue(1, 0.0);
   SetIndexEmptyValue(2, 0.0);
   SetIndexEmptyValue(3, 0.0);

   // Set indicator styles and colors
   SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, 2, clrGreen);
   SetIndexStyle(1, DRAW_LINE, STYLE_SOLID, 2, clrRed);
   SetIndexStyle(2, DRAW_ARROW, STYLE_SOLID, 3, clrGreen);
   SetIndexStyle(3, DRAW_ARROW, STYLE_SOLID, 3, clrRed);

   // Set arrow codes
   SetIndexArrow(2, 233); // Up arrow for buy
   SetIndexArrow(3, 234); // Down arrow for sell

   // Set drawing begin
   SetIndexDrawBegin(0, ATR_Period);
   SetIndexDrawBegin(1, ATR_Period);
   SetIndexDrawBegin(2, ATR_Period);
   SetIndexDrawBegin(3, ATR_Period);

   // Set indicator names
   IndicatorShortName("Chandelier Exit ("+IntegerToString(ATR_Period)+","+DoubleToString(ATR_Multiplier,1)+")");

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Clean up all objects created by this indicator
   ObjectsDeleteAll(0, "CE_");

   // Print message
   Print("Chandelier Exit indicator removed");
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   // Check for minimum required bars
   if(rates_total < ATR_Period + 1) return(0);

   // Calculate start position
   int limit;
   if(prev_calculated == 0)
      limit = rates_total - ATR_Period - 1;
   else
      limit = rates_total - prev_calculated;

   // Ensure we don't recalculate all bars
   if(limit > rates_total - 1) limit = rates_total - 1;

   // Main calculation loop
   for(int i = limit; i >= 0; i--)
   {
      // Calculate ATR
      double atr = iATR(NULL, 0, ATR_Period, i) * ATR_Multiplier;

      // Calculate highest and lowest values
      double highest = 0;
      double lowest = 0;

      if(UseClosePrice)
      {
         highest = High[iHighest(NULL, 0, MODE_CLOSE, ATR_Period, i)];
         lowest = Low[iLowest(NULL, 0, MODE_CLOSE, ATR_Period, i)];
      }
      else
      {
         highest = High[iHighest(NULL, 0, MODE_HIGH, ATR_Period, i)];
         lowest = Low[iLowest(NULL, 0, MODE_LOW, ATR_Period, i)];
      }

      // Calculate long and short stops
      double longStop = highest - atr;
      double shortStop = lowest + atr;

      // Apply trailing stop logic
      if(i < rates_total - 1)
      {
         double longStopPrev = (LongStopBuffer[i+1] == 0) ? longStop : LongStopBuffer[i+1];
         double shortStopPrev = (ShortStopBuffer[i+1] == 0) ? shortStop : ShortStopBuffer[i+1];

         if(Close[i+1] > longStopPrev)
            longStop = MathMax(longStop, longStopPrev);

         if(Close[i+1] < shortStopPrev)
            shortStop = MathMin(shortStop, shortStopPrev);
      }

      // Determine direction
      int prevDirection = Direction;
      if(i < rates_total - 1)
      {
         double longStopPrev = (LongStopBuffer[i+1] == 0) ? longStop : LongStopBuffer[i+1];
         double shortStopPrev = (ShortStopBuffer[i+1] == 0) ? shortStop : ShortStopBuffer[i+1];

         if(Close[i] > shortStopPrev)
            Direction = 1;
         else if(Close[i] < longStopPrev)
            Direction = -1;
      }

      // Store values in buffers
      if(Direction == 1)
      {
         LongStopBuffer[i] = longStop;
         ShortStopBuffer[i] = 0;
      }
      else
      {
         LongStopBuffer[i] = 0;
         ShortStopBuffer[i] = shortStop;
      }

      // Check for signals
      bool buySignal = (Direction == 1 && prevDirection == -1);
      bool sellSignal = (Direction == -1 && prevDirection == 1);

      // Set signal buffers
      BuySignalBuffer[i] = 0;
      SellSignalBuffer[i] = 0;

      if(buySignal)
      {
         BuySignalBuffer[i] = longStop;

         // Create text label for buy signal
         if(ShowLabels)
         {
            string labelName = "CE_BuyLabel_" + TimeToString(time[i]);
            if(ObjectFind(labelName) < 0) // Only create if it doesn't exist
            {
               ObjectCreate(labelName, OBJ_TEXT, 0, time[i], longStop - 10 * Point);
               ObjectSetText(labelName, "Buy", 10, "Arial Bold", clrGreen);
               ObjectSetInteger(0, labelName, OBJPROP_SELECTABLE, false);
               ObjectSetInteger(0, labelName, OBJPROP_BACK, false);
               ObjectSetInteger(0, labelName, OBJPROP_HIDDEN, false);
            }
         }
         LastSignal = 1;
      }
      else if(sellSignal)
      {
         SellSignalBuffer[i] = shortStop;

         // Create text label for sell signal
         if(ShowLabels)
         {
            string labelName = "CE_SellLabel_" + TimeToString(time[i]);
            if(ObjectFind(labelName) < 0) // Only create if it doesn't exist
            {
               ObjectCreate(labelName, OBJ_TEXT, 0, time[i], shortStop + 10 * Point);
               ObjectSetText(labelName, "Sell", 10, "Arial Bold", clrRed);
               ObjectSetInteger(0, labelName, OBJPROP_SELECTABLE, false);
               ObjectSetInteger(0, labelName, OBJPROP_BACK, false);
               ObjectSetInteger(0, labelName, OBJPROP_HIDDEN, false);
            }
         }
         LastSignal = -1;
      }

      // Highlight state (only for the most recent bar)
      if(i == 0 && HighlightState)
      {
         // Remove old rectangle
         ObjectDelete("CE_Fill");

         // Create new rectangle
         if(Direction == 1)
            CreateRectangle("CE_Fill", time[i], (High[i] + Low[i])/2, longStop, clrGreen, 90);
         else
            CreateRectangle("CE_Fill", time[i], (High[i] + Low[i])/2, shortStop, clrRed, 90);
      }
   }

   return(rates_total);
}

//+------------------------------------------------------------------+
//| Create a rectangle for highlighting                              |
//+------------------------------------------------------------------+
void CreateRectangle(string name, datetime time, double midPrice, double stopPrice, color clr, int transparency)
{
   color fillColor = clr;
   if(transparency > 0)
   {
      // MQL4 doesn't have direct color transparency, so we approximate with different colors
      if(clr == clrGreen)
         fillColor = C'225,255,225';
      else if(clr == clrRed)
         fillColor = C'255,225,225';
   }

   ObjectCreate(name, OBJ_RECTANGLE, 0, time, midPrice, time, stopPrice);
   ObjectSetInteger(0, name, OBJPROP_COLOR, fillColor);
   ObjectSetInteger(0, name, OBJPROP_BACK, true);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, false);
   ObjectSetInteger(0, name, OBJPROP_FILL, true);
}
