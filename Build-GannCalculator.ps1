# PowerShell script to build Gann Square of 9 Calculator
# Run with: PowerShell -ExecutionPolicy Bypass -File Build-GannCalculator.ps1

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Gann Square of 9 Calculator Builder" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check Java installation
Write-Host "Checking Java installation..." -ForegroundColor Yellow
if (-not (Test-Command "java")) {
    Write-Host "ERROR: Java is not installed or not in PATH!" -ForegroundColor Red
    Write-Host "Please install Java from: https://www.java.com/download/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-Command "javac")) {
    Write-Host "ERROR: Java compiler (javac) not found!" -ForegroundColor Red
    Write-Host "Please install Java Development Kit (JDK)" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Get Java version
$javaVersion = java -version 2>&1 | Select-String "version"
Write-Host "✓ Java found: $javaVersion" -ForegroundColor Green

# Compile Java source
Write-Host ""
Write-Host "Step 1: Compiling Java source code..." -ForegroundColor Yellow
try {
    javac GannSquareOf9Calculator.java
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Compilation successful!" -ForegroundColor Green
    } else {
        throw "Compilation failed"
    }
} catch {
    Write-Host "ERROR: Compilation failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Create JAR file
Write-Host ""
Write-Host "Step 2: Creating JAR file..." -ForegroundColor Yellow
try {
    jar cfm GannSquareOf9Calculator.jar MANIFEST.MF *.class
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ JAR file created successfully!" -ForegroundColor Green
    } else {
        throw "JAR creation failed"
    }
} catch {
    Write-Host "ERROR: JAR creation failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Test the JAR file
Write-Host ""
Write-Host "Step 3: Testing JAR file..." -ForegroundColor Yellow
Write-Host "Starting the application..." -ForegroundColor Cyan
Start-Process -FilePath "java" -ArgumentList "-jar", "GannSquareOf9Calculator.jar"

# Display results
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Files created:" -ForegroundColor Cyan
Write-Host "- GannSquareOf9Calculator.class (compiled bytecode)" -ForegroundColor White
Write-Host "- GannSquareOf9Calculator.jar (executable JAR)" -ForegroundColor White
Write-Host ""
Write-Host "To create an EXE file:" -ForegroundColor Yellow
Write-Host "1. Install Launch4j from: https://launch4j.sourceforge.net/" -ForegroundColor White
Write-Host "2. Run: create-exe.bat" -ForegroundColor White
Write-Host ""
Write-Host "To run the application:" -ForegroundColor Yellow
Write-Host "- Double-click GannSquareOf9Calculator.jar" -ForegroundColor White
Write-Host "- Or run: java -jar GannSquareOf9Calculator.jar" -ForegroundColor White
Write-Host "- Or use: run.bat" -ForegroundColor White

Read-Host "`nPress Enter to exit"
