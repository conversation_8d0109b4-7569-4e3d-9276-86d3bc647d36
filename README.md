# Gann Square of 9 Calculator

A standalone Windows application for calculating support and resistance levels based on W<PERSON><PERSON><PERSON> Gann's Square of 9 theory for financial markets.

## Features

- **Offline Operation**: Works completely without internet connection
- **Comprehensive Calculations**: Support and resistance levels for multiple angles (45°, 90°, 135°, 180°, 225°, 270°, 315°, 360°)
- **Visual Interface**: Color-coded buy/sell recommendations
- **Professional Design**: Clean, easy-to-use graphical interface
- **Educational Content**: Built-in explanations of <PERSON><PERSON>'s Square of 9 theory

## Quick Start

### Option 1: Run the JAR file (Recommended)
1. Ensure Java is installed on your system
2. Double-click `GannSquareOf9Calculator.jar`
3. Or run `run.bat`

### Option 2: Create EXE file
1. Install Launch4j from: https://launch4j.sourceforge.net/
2. Open Launch4j
3. Load the configuration file: `launch4j-config.xml`
4. Click "Build wrapper" to create `GannSquareOf9Calculator.exe`

## Installation Requirements

### Java Runtime Environment (JRE)
- **Minimum**: Java 8 or higher
- **Download**: https://www.java.com/download/
- **Note**: Most Windows systems already have Java installed

### For Development/Building
- **Java Development Kit (JDK)**: Required only if you want to modify the code
- **Download**: https://www.oracle.com/java/technologies/downloads/

## Building from Source

1. **Compile the Java code:**
   ```
   javac GannSquareOf9Calculator.java
   ```

2. **Create JAR file:**
   ```
   jar cfm GannSquareOf9Calculator.jar MANIFEST.MF *.class
   ```

3. **Or use the automated build script:**
   ```
   build.bat
   ```

## Usage Instructions

1. **Launch the application**
2. **Enter a price** in the input field (e.g., current stock price, forex rate, etc.)
3. **Click "Calculate Levels"**
4. **Review the results:**
   - **Green levels**: Potential buy zones
   - **Red levels**: Potential sell zones
   - **Strongest levels**: 90° and 270° angles

## File Structure

```
GannSquareOf9Calculator/
├── GannSquareOf9Calculator.java    # Source code
├── GannSquareOf9Calculator.jar     # Executable JAR file
├── GannSquareOf9Calculator.exe     # Windows executable (after Launch4j)
├── MANIFEST.MF                     # JAR manifest file
├── launch4j-config.xml             # Launch4j configuration
├── build.bat                       # Build automation script
├── run.bat                         # Simple run script
└── README.md                       # This file
```

## Troubleshooting

### "Java is not installed" Error
- Download and install Java from: https://www.java.com/download/
- Restart your computer after installation

### "JAR file not found" Error
- Run `build.bat` first to create the JAR file
- Ensure all files are in the same directory

### Application doesn't start
- Check that Java is properly installed: `java -version`
- Try running from command prompt: `java -jar GannSquareOf9Calculator.jar`

## About Gann Square of 9

The Square of 9 is a mathematical tool developed by legendary trader W.D. Gann. It maps numbers in a spiral pattern starting from the center and moving outward. Different angles from the center point create lines that intersect specific numbers, which represent potential support and resistance levels in financial markets.

### Key Concepts:
- **Center Point**: Your input price
- **Angles**: 45°, 90°, 135°, 180°, 225°, 270°, 315°, 360°
- **Strongest Levels**: 90° and 270° angles typically provide the most significant support/resistance
- **Price Calculation**: Based on square root relationships and angular mathematics

## License

This software is provided as-is for educational and analysis purposes. Use at your own risk for trading decisions.

## Support

For technical issues:
1. Ensure Java is properly installed
2. Check that all files are in the same directory
3. Try running the application from command prompt for detailed error messages
