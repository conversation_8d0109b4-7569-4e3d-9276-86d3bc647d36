@echo off
echo Starting Gann Square of 9 Calculator...
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed!
    echo Please install Java Runtime Environment (JRE) 8 or higher.
    echo Download from: https://www.java.com/download/
    echo.
    pause
    exit /b 1
)

REM Check if JAR file exists
if not exist "GannSquareOf9Calculator.jar" (
    echo ERROR: GannSquareOf9Calculator.jar not found!
    echo Please run build.bat first to create the JAR file.
    echo.
    pause
    exit /b 1
)

REM Run the application
java -jar GannSquareOf9Calculator.jar

REM Keep window open if there's an error
if %errorlevel% neq 0 (
    echo.
    echo Application exited with error code: %errorlevel%
    pause
)
