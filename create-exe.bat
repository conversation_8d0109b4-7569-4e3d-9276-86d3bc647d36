@echo off
echo ========================================
echo Gann Square of 9 Calculator EXE Builder
echo ========================================
echo.

REM Check if Launch4j is available
where launch4j >nul 2>&1
if %errorlevel% neq 0 (
    echo Launch4j not found in PATH. Checking common installation locations...
    
    set "LAUNCH4J_PATH="
    if exist "C:\Program Files\Launch4j\launch4j.exe" (
        set "LAUNCH4J_PATH=C:\Program Files\Launch4j\launch4j.exe"
    ) else if exist "C:\Program Files (x86)\Launch4j\launch4j.exe" (
        set "LAUNCH4J_PATH=C:\Program Files (x86)\Launch4j\launch4j.exe"
    ) else if exist "%USERPROFILE%\AppData\Local\Launch4j\launch4j.exe" (
        set "LAUNCH4J_PATH=%USERPROFILE%\AppData\Local\Launch4j\launch4j.exe"
    )
    
    if "%LAUNCH4J_PATH%"=="" (
        echo.
        echo ERROR: Launch4j not found!
        echo.
        echo Please install Launch4j from: https://launch4j.sourceforge.net/
        echo.
        echo Installation instructions:
        echo 1. Download Launch4j from the website above
        echo 2. Install it to the default location
        echo 3. Run this script again
        echo.
        echo Alternative: Add Launch4j to your system PATH
        echo.
        pause
        exit /b 1
    )
    
    echo Found Launch4j at: %LAUNCH4J_PATH%
) else (
    set "LAUNCH4J_PATH=launch4j"
    echo Launch4j found in PATH
)

REM Check if JAR file exists
if not exist "GannSquareOf9Calculator.jar" (
    echo.
    echo JAR file not found. Building it first...
    call build.bat
    if %errorlevel% neq 0 (
        echo Failed to build JAR file!
        pause
        exit /b 1
    )
)

echo.
echo Creating Windows executable...
echo Using configuration: launch4j-config.xml

REM Create the EXE using Launch4j
"%LAUNCH4J_PATH%" launch4j-config.xml

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to create EXE file!
    echo Check the Launch4j configuration and try again.
    pause
    exit /b 1
)

REM Check if EXE was created
if exist "GannSquareOf9Calculator.exe" (
    echo.
    echo ========================================
    echo SUCCESS! EXE file created successfully!
    echo ========================================
    echo.
    echo File: GannSquareOf9Calculator.exe
    echo Size: 
    dir GannSquareOf9Calculator.exe | findstr "GannSquareOf9Calculator.exe"
    echo.
    echo The executable is now ready to use!
    echo You can distribute this EXE file without requiring users to install Java separately.
    echo.
    echo Testing the EXE file...
    start GannSquareOf9Calculator.exe
) else (
    echo.
    echo ERROR: EXE file was not created!
    echo Check the Launch4j output for errors.
)

echo.
pause
